# def vowel_count(word):
#     return sum(1 for char in word if char.lower() in "aeiou")

# words = input().split()
# print(" ".join([f"{word}({vowel_count(word)})" for word in words]))


# def count_vowels(word):
#     vowels = "AEIOUaeiou"
#     return sum(1 for char in word if char in vowels)

# Read input
#input_string = input()

# Process each word and count vowels
# words = "word1 word2 word3 word4 word5"
# result = []

# for word in words:
#     vowel_count = count_vowels(word)
#     result.append(f"{word}({vowel_count})")

# # Print the output while maintaining spaces
# print(" ".join(result))


# sentence = "word1 word2 word3 word4 word5"
# vowels="aeiou"
# words = sentence.split()
# result=[]

# for word in words:
#         for character in word:
#                 ctr=0
#                 if character.lower() in vowels:
#                         ctr+=1
#         result.append([f"{word}     ({ctr})"])

# print("result : ", result)


# l=(11,22,33,32,22,33)
# even_count=0
# odd_count=0

# {"even" : even_count, "odd" : odd_count}

# l=set(l)

# print(
#     {
#     "even_count" : sum(1 for x in l if (x%2 == 0)),
#     "odd_count" : sum(1 for x in l if (x%2 != 0))
#     }
#     )
