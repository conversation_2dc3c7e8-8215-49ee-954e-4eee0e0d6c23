# Write a function remove_elements_at_two_indices that:

# Takes a list l and two indices i1 and i2

# Removes the elements at these two indices from the list l

# Modifies the list l in place (does not return anything)

# 📌 Assumptions:
# The indices i1 and i2 are:

# Non-negative

# Unique (not the same index)

# def remove_elements_at_two_indices(l: list, i1: int, i2: int):
#     '''Remove elements at two specified indices in the list.

#     Args:
#         l : list - input list
#         i1, i2 : int - indices of elements to remove
#                   both are non-negative and unique

#     Returns:
#         None - modifies the list in place
#     '''
#     i1, i2 = sorted([i1, i2], reverse=True)
#     print(l)
#     del l[i1]
#     print(l)
#     del l[i2]
#     print(l)

# # Example usage:
# l = [1, 2, 3, 4, 5, 6, 7]
# i1 = 5
# i2 = 1
# remove_elements_at_two_indices(l, i1, i2)



# Write a function sum_of_squares_of_even that:
# Takes a list of integers nums
# Returns the sum of the squares of all even numbers in the list


# def sum_of_squares_of_even(nums: list) -> int:
#     '''Return the sum of squares of all even numbers in the list.

#     Args:
#         nums : list - list of integers

#     Returns:
#         int - sum of squares of all even numbers
#     '''

#     # 1 - basic solution
#     # total = 0
#     # for num in nums:
#     #     if num % 2 == 0:
#     #         total += num ** 2
#     # return total

#     # 2 - comprehensions
#     return sum([num**2 for num in nums if num % 2 == 0])

#     # 3 - functional
#     # return sum(map(lambda x: x**2, filter(lambda x: x % 2 == 0, nums)))

# # Example usage:
# nums = [1, 2, 3, 4, 5, 6]
# print(sum_of_squares_of_even(nums))
# Output: 56


# Write a program that takes a passage with n lines of text as input. For each line, convert all vowels (a, e, i, o, u) to uppercase and all other characters to lowercase.

# Note:
# This is an I/O type question, so you must write the full code for:

# Input Format:
# The first line contains an integer n, the number of lines.

# The next n lines contain the passage (text lines).

# Output Format:
# Print the passage with vowels in uppercase and all other characters in lowercase.

# Hint:
# Use the upper() and lower() methods of str to convert characters as needed.

# Example:

# Input:

# 2
# Hello World
# Python Programming

# Output:

# hEllO wOrld
# pythOn prOgrAmmIng

# n = int(input())
# for t in range(n):
#     line = input()
#     print("".join([c.upper() if c.lower() in "aeiou" else c.lower() for c in line]))



# Student Marks Filter
# Write a function to filter student roll numbers based on the following criteria, given data in the format of a list of tuples:
# Each tuple contains: (rollno: str, marks: int)

# Filter Criteria:
# 'above_average': Students with marks greater than or equal to the average.

# 'below_average': Students with marks less than the average.

# 'fail': Students with marks less than 40.

# 'toppers': Students with marks 90 or above.

# None: Return all roll numbers.

# Any other value: Return None.

# The roll numbers must be returned in the same order as in the input data.


def get_filter_func(criteria, data):
    if criteria is None:
        return lambda x: True
    if criteria == 'fail':
        return lambda x: x<40
    if criteria == 'toppers':
        return lambda x: x>=90

    marks = [row[1] for row in data]
    avg = sum(marks)/len(marks)
    if criteria == 'above_average':
        return lambda x: x>=avg
    if criteria == 'below_average':
        return lambda x: x<avg

def get_roll_nos(data:list, criteria=None):
    '''
    Filter roll numbers based on criteria.

    Args:
        data (list): List of tuples with roll number and marks.
        criteria (str, optional): The criteria for filtering.

    Returns:
        list: List of roll numbers matching the criteria or None if invalid criteria.
    '''
    filter_func = get_filter_func(criteria, data)
    if filter_func is not None:
        return [rollno for rollno, marks in data if filter_func(marks)]


data = [("101", 85), ("102", 75), ("103", 45), ("104", 95), ("105", 35)]

print(get_roll_nos(data, 'above_average'))  # ➞ ["101", "102", "104"]
print(get_roll_nos(data, 'below_average'))  # ➞ ["103", "105"]
print(get_roll_nos(data, 'None'))           # ➞ None
print(get_roll_nos(data, 'fail'))           # ➞ ["105"]
print(get_roll_nos(data, 'toppers'))        # ➞ ["104"]
print(get_roll_nos(data))                   # ➞ ["101", "102", "103", "104", "105"]
