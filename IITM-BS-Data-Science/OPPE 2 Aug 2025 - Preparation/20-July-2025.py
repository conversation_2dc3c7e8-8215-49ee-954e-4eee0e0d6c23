from collections import Counter
nums = [int(input()) for i in range(int(input()))]

value_counts = dict(Counter(nums))
print("type(Counter(nums)) : ", type(Counter(nums)))
print("Counter(nums) : ", Counter(nums))
print("Value counts:", value_counts)
print("type(value_counts) : ", type(value_counts))
    
max_frequency = max(value_counts.values())

most_common_values = sorted([
    value
    for value, count in value_counts.items()
    if count == max_frequency
])

print(most_common_values)
