# Call Log Analysis
# You are provided with a call log history represented as a list of dictionaries. Each dictionary contains the following keys:

# name (str): The name of the caller.

# duration (int): The duration of the call in seconds.

# There will be multiple entries with the same name in the dataset. Implement a function
# process_call_logs(call_logs, task)
# where task is one of the following:

# NOTE: This is a function-type question; you don’t have to take input or print the output, just complete the required function definition.

# 1. get_call_counts
# Returns a dictionary with caller names as keys and the count of their calls as values.

# 2. get_total_call_durations
# Returns a dictionary with caller names as keys and the total duration of their calls as values.

# 3. most_frequent_caller
# Returns the name of the caller with the highest number of calls.

# If there is a tie, the name with the most total duration is returned.

# Example
# python
# Copy
# Edit
# call_log = [
#     {"name": "Alice", "duration": 300},
#     {"name": "<PERSON>", "duration": 200},
#     {"name": "<PERSON>", "duration": 100},
#     {"name": "<PERSON>", "duration": 400},
#     {"name": "<PERSON>", "duration": 300}
# ]
# get_call_count → {'Alice': 2, '<PERSON>': 2, '<PERSON>': 1}

# get_total_call_durations → {'Alice': 400, 'Bob': 600, '<PERSON>': 300}

# most_frequent_caller → 'Bob'

# (<PERSON> and Bob have the same number of calls but Bob has the most total duration.)

# def get_call_counts(call_logs):
#     call_counts = {}
#     for log in call_logs:
#         if log['name'] not in call_counts:
#             call_counts[log['name']] = 0
#         call_counts[log['name']] += 1
#     return call_counts


# def get_total_call_durations(call_logs):
#     total_durations = {}
#     for log in call_logs:
#         if log['name'] not in total_durations:
#             total_durations[log['name']] = 0
#         total_durations[log['name']] += log['duration']
#     return total_durations


# def most_frequent_caller(call_logs):
#     call_counts = get_call_counts(call_logs)
#     total_durations = get_total_call_durations(call_logs)
#     return max(
#         call_counts,
#         key=lambda name: (call_counts[name], total_durations[name])
# # if both counts are equal, the one with the higher total duration wins    
    
#     )

# def process_call_logs(call_logs, task):
#     if task == 'get_call_counts':
#         return get_call_counts(call_logs)
#     elif task == 'get_total_call_durations':
#         return get_total_call_durations(call_logs)
#     elif task == 'most_frequent_caller':
#         return most_frequent_caller(call_logs)
#     else:
#         raise ValueError("Invalid task provided.")
# # Example usage:
# call_log = [
#     {"name": "Alice", "duration": 800},
#     {"name": "Bob", "duration": 1200},
#     {"name": "Alice", "duration": 100},
#     {"name": "Bob", "duration": 400},
#     {"name": "Charlie", "duration": 300}
# ]
# print(process_call_logs(call_log, 'get_call_counts'))  # {'Alice': 2, 'Bob': 2, 'Charlie': 1}
# print(process_call_logs(call_log, 'get_total_call_durations'))  # {'Alice': 400, 'Bob': 600, 'Charlie': 300}
# print(process_call_logs(call_log, 'most_frequent_caller'))  # 'Bob'
# Note: The example usage is commented out to adhere to the instruction of not taking input or printing output directly.
# You can uncomment and run it in your local environment to test the function.  


# Input:

# first line
# second line
# third line
# fourth line
# 1-3,2-4,3-2,4-1


# This writes the stdin to the input file
# import tempfile
# import sys

# _, filename = tempfile.mkstemp(prefix="case")
# with open(filename, 'w') as f:
#     f.write(sys.stdin.read())

# with open(filename) as f:
#     lines = f.readlines()
#     lines, ordering = lines[:-1], lines[-1].strip().split(",")
#     # Parse ordering like "1-3" into source-target pairs
#     order_pairs = [map(int, pair.split("-")) for pair in ordering]
#     # Create new list with same length as input
#     result = [""] * len(lines)
#     # Place each line according to source-target mapping
#     for src, tgt in order_pairs:
#         result[tgt-1] = lines[src-1]
#     print(*result, sep="")


# Product of Sum and Absolute Difference of Digits in a Two-Digit Number
# Given a two-digit number, calculate the product of:

# The sum of its digits.

# The absolute difference between its digits.

# Assume the input is a two-digit number.

# NOTE: This is a function type question, you don’t have to take input or print the output, just have to complete the required function definition.

# Input:
# A two-digit integer.

# Output:
# An integer representing the product of the sum and absolute difference of its digits.

# Example
# Input

# Copy
# Edit
# 54
# Output

# Copy
# Edit
# 9
# Explanation

# Digits are 5 and 4.

# Sum = 5 + 4 = 9.

# Absolute Difference = |5 - 4| = 1.

# Product = 9 × 1 = 9.


# def product_of_sum_and_abs_diff_of_digits(num: int):
#     """
#     Returns the product of the sum and absolute difference of digits of a two digit number.

#     Assume number is a two digit number.

#     Args:
#         num (int) - The given number

#     Returns:
#         The required product.

#     Examples:
#     >>> product_of_sum_and_abs_diff_of_digits(38)
#     55
#     >>> product_of_sum_and_abs_diff_of_digits(55)
#     0
#     """
#     a, b = num // 10, num % 10  # Extract digits
#     return (a + b) * abs(a - b)

# # Test cases
# print(product_of_sum_and_abs_diff_of_digits(38))  # Should output: 55 (sum=11, diff=5, product=55)
# print(product_of_sum_and_abs_diff_of_digits(55))  # Should output: 0 (sum=10, diff=0, product=0)
# print(product_of_sum_and_abs_diff_of_digits(72))  # Should output: 45 (sum=9, diff=5, product=45)
# print(product_of_sum_and_abs_diff_of_digits(90))  # Should output: 81 (sum=9, diff=9, product=81)

# Check Divisibility by Last Two Digits
# Write a function that checks whether a given number is divisible by both of its last two digits.

# Return False if any of the last two digits is zero.

# NOTE: This is a function type question, you don’t have to take input or print the output, just have to complete the required function definition.
# Input: A positive integer num with at least two digits.

# Output: True if the number is divisible by both of its last two digits, otherwise False.

# Examples
# 1236 -> True, 1236 is divisible by both 3 and 6.

# 345 -> False, 345 is divisible by 5 but not divisible by 4.

# 748 -> False, 748 is divisible by 4 but not divisible by 8.

# 740 -> False, the last digit is 0.

# def is_divisible_by_last_two_digits(num: int):
#     """
#     Checks if the given number is divisible by both of its last two digits.

#     Return False if any of the last two digits is 0.

#     Args:
#         num (int): The number to check.

#     Returns:
#         bool: True if divisible by both last two digits, False otherwise.
#     """
#     a, b = str(num)[-2:]
#     print(a,b)
#     a, b = int(a), int(b)
#     return a != 0 and b != 0 and num % a == 0 and num % b == 0

# print(is_divisible_by_last_two_digits(1236))  # True
# print(is_divisible_by_last_two_digits(345))   # False
# print(is_divisible_by_last_two_digits(748))   # False
# print(is_divisible_by_last_two_digits(740))   # False

# Swap the Last Chars in Dictionary Values
# Task:
# Write a function that swaps the last characters of the values corresponding to two specified keys in a dictionary.

# Assumptions:
# The values in the dictionary are strings.

# Both k1 and k2 are present in the dictionary.

# The values corresponding to k1 and k2 are non-empty strings.

# Input:
# A dictionary d where values are strings.

# Two keys: k1 and k2.

# Output:
# Modify the dictionary in-place by swapping the last characters of the values of the given keys.

# Function returns None.

# Example 1

# d = {"first": "apple", "second": "mango", "third": "banana"}
# swap_last_chars_of_values(d, "first", "second")
# # d becomes: {'first': 'applo', 'second': 'mange', 'third': 'banana'}

# Example 2

# d = {"key1": "hello", "key2": "world"}
# swap_last_chars_of_values(d, "key1", "key2")
# # d becomes: {'key1': 'helld', 'key2': 'worlo'}

# Function Definition:

# def swap_last_chars_of_values(d, k1, k2):
#     ...

# def swap_last_chars_of_values(d: dict, k1, k2):
#     """
#     Swap the last chars of the values of given keys in the dict.

#     Args:
#         d (dict): The dictionary with string values.
#         k1: The first key.
#         k2: The second key.

#     Returns:
#         None - modifies the dictionary in-place.
#     """
#     a, b = d[k1], d[k2]  # to simplify the notation
#     d[k1], d[k2] = a[:-1] + b[-1], b[:-1] + a[-1]

# # # Example usage:
# d = {"first": "apple", "second": "mango", "third": "banana"}
# swap_last_chars_of_values(d, "first", "second")
# print(d)  # Output: {'first': 'applo', 'second': 'mange', 'third': 'banana'}


# Get Words That Come After "the"
# Task:
# Write a function to find all the words that immediately follow the word "the" in a given sentence.

# Constraints:
# The search should be case-insensitive.


# Words are separated by spaces.

# The output should include every word that immediately follows a variation of "the" ("The", "THE", "tHe", etc.).

# Output:
# Return a list of all words that immediately follow the word "the", considering case-insensitivity.

# Example 1:
# "The key and the lock is there" → ['key', 'lock']

# Example 2:
# "The the and the The" → ['the', 'and', 'The']

#Function Signature:

#def get_words_after_the(sentence):


# def get_words_after_the(sentence: str) -> list:
#     words = sentence.split()
#     words_after_the = []
#     for i in range(1, len(words)):
#         if words[i - 1].lower() == 'the':
#             words_after_the.append(words[i])
#     return words_after_the

# # # Example usage:
# sentence = "The key and the lock is there"
# print(*get_words_after_the(sentence))  # Output: ['key', 'lock']
# sentence = "The the and the The"
# print(get_words_after_the(sentence))  # Output: ['the', 'and', 'The']


# Ceil Marks to Nearest Tens if Close

# Write a program to process roll numbers and marks for students. If a student's marks have a unit digit of 8 or 9, round their marks up to the nearest tens. Otherwise, keep the marks unchanged. Print only the roll number and updated marks for students whose marks were updated.

# NOTE: This is an I/O type question, you need to write the whole code for taking input and printing the output.

# Input Format
# The first line contains the number of lines of student data n.

# Next n lines contains the student data with roll number and marks separated by space.

# Output Format
# Student data for the roll number whose marks has been updated in the format of roll number and separated by comma.

# Examples
# 48 -> 50

# 69 -> 70

# 70 -> 70

# 37 -> 37

# n = int(input())
# for i in range(n):
#     roll, marks = map(int, input().split())
#     if marks % 10 in [8, 9]:
#         marks = (marks // 10 + 1) * 10
#     if marks != (marks // 10 + 1) * 10:
#         print(roll, marks)




# Cartesian Points Processing
# Given a set of tuples of ints representing points in Cartesian coordinates, write a function that performs the following tasks:

# sort_close_to_y_axis:
# Returns a sorted list of the points based on the absolute value of their x-coordinate (distance from the y-axis). The point with the lower angle in polar coordinates comes first in case of ties.

# closest_point_to_origin:
# Find the closest point to the origin (0,0) based on the Manhattan distance. If there are multiple points at the same distance, return the one with the lower angle in polar coordinates.

# group_by_quadrant:
# Return a dictionary where the keys are quadrant numbers (1, 2, 3, 4) and the values are sets of points in those quadrants. Do not include keys for quadrants with no points.

# Note: The angle is computed using math.atan2(y, x) where x and y are the Cartesian coordinates. math.atan2 returns the angle in the range of -pi to pi. You can use the get_angle function from the template.

# Assume no points are on the axes or origin and the coordinates are integers.

# Hint: sorted and min functions can be used with the key argument to make the code simpler.

# Definitions:
# Manhattan distance between two points (x1, y1) and (x2, y2) is
# |x1 - x2| + |y1 - y2|.

# NOTE: This is a function type question, you don’t have to take input or print the output, just have to complete the required function definition.



# import math

# def get_angle(x, y):
#     return math.atan2(y, x)

# def sort_close_to_y_axis(points):
#     # Sort first by distance from y-axis, then by angle
#     return sorted(points, key=lambda p: (abs(p[0]), -p[1] if p[0] == 0 else get_angle(p[0], p[1])))

# def closest_point_to_origin(points):
#     # Manhattan distance + angle as tiebreaker
#     return min(points, key=lambda p: (abs(p[0]) + abs(p[1]), get_angle(p[0], p[1])))

# def get_quadrant(point):
#     x, y = point
#     if x > 0:
#         return 1 if y > 0 else 4
#     return 2 if y > 0 else 3

# def group_by_quadrant(points):
#     result = {}
#     for point in points:
#         q = get_quadrant(point)
#         if q not in result:
#             result[q] = set()
#         result[q].add(point)
#     return result

# def process_points(points: set, task: str):
#     """Process the points according to the given task.
    
#     Args:
#         points (set[tuple[int,int]]): Set of points in cartesian coordinates
#         task (str): One of 'sort_close_to_y_axis', 'closest_point_to_origin', 'group_by_quadrant'
    
#     Returns:
#         The output of the corresponding task
#     """
#     tasks = {
#         "sort_close_to_y_axis": sort_close_to_y_axis,
#         "closest_point_to_origin": closest_point_to_origin,
#         "group_by_quadrant": group_by_quadrant
#     }
    
#     if task not in tasks:
#         raise ValueError("Invalid task")
        
#     return tasks[task](points)

# points = {
#     (2, 5), (-2, 6), (-2, 7), (-1, 3),
#     (-3, -4), (4, -6), (0, 7)
# }
# task = "sort_close_to_y_axis"
# # task = "closest_point_to_origin"
# # task = "group_by_quadrant"

# output = [
#     (0, 7), (-1, 3), (2, 5), (-2, 7),
#     (-2, 6), (-3, -4), (4, -6)
# ]

# print(process_points(points, task))


# Fill the first n blanks with 1 to n
# Write a program to fill the first n blanks (represented by underscores _) in a text with numbers from 1 to n.

# The first line of the input contains the integer n, the number of blanks to fill.

# Subsequent lines contain the text, with blanks represented as _.

# Fill the blanks sequentially with numbers from 1 to n.

# If there are more blanks than n, the remaining blanks remain unchanged.

# NOTE: This is a file-in-stdout type question where the input is read from the file and the output is printed in the standard output.

# Example

# Input

# 11
# a_bc__d__e
# f_g_h__i__j
# k___l_m__n


# Output

# 11
# a1bc23d456e
# f7g89h1011i__j
# k___l_m__n

# This writes the stdin to the input file

# import tempfile
# import sys
# _, filename = tempfile.mkstemp(prefix="case")
# with open(filename, 'w') as f:
#     f.write(sys.stdin.read())


# with open(filename) as f:
#     n = int(f.readline())
#     i = 1
#     for line in f:
#         filled_line = ""
#         for char in line:
#             if char == "_" and i <= n:
#                 filled_line += str(i)
#                 i += 1
#             else:
#                 filled_line += char
#         print(filled_line, end="")

# 10
# My n___ is Yuvara_____ 
# what is ______  name
# Becaue ____ i____ghtheh ____llll
# My n123 is Yuvara45678 
# what is 910____  name
# Becaue ____ i____ghtheh ____llll

# ✅ Function Name: is_odd_length_palindrome
# ✅ Description:
# Write a function is_odd_length_palindrome that takes a string s and returns:

# True if s is a palindrome and has odd length

# False otherwise

# ✅ Examples:
# "hello" → False (not a palindrome)

# "noon" → False (palindrome, but even length)

# "nun" → True (palindrome and odd length)

# def is_odd_length_palindrome(s: str) -> bool:
    # print(s[::-1])
    # print(s[-1::])
    # print(s[::1])
    # print(s[1::])
    # print(s[:-1])
    # print(s[-1:])
    # print(s[:1])
    # print(s[1:])
    # return len(s) % 2 == 1 and s == s[::-1]

# print(is_odd_length_palindrome("hello"))  # False
# print(is_odd_length_palindrome("noon"))   # False
# print(is_odd_length_palindrome("nun"))    # True