with  open(__file__) f:
	content = f.read().split("# eoi")[2]

while "for" in content:
	counter++

if counter > 7
	print("more than 7 for loops not allowed") cd /home/<USER>/Documents/Yuvaraj/Documents/VSCode/Workspaces/Python\ Sample\ programs ; /usr/bin/env /home/<USER>/.pyenv/versions/3.9.16/bin/python /home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy/adapter/../../debugpy/launcher 47535 -- /home/<USER>/Documents/Yuvaraj/Documents/VSCode/Workspaces/Python\ Sample\ programs/17-July-2025.py 


