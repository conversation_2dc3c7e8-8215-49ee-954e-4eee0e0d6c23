# Note this prefix code is to verify that you are not using any for loops in this exercise. This won't affect any other functionality of the program.
with open(__file__) as f:
    content = f.read().split("# <eoi>")[2]
if "for " in content:
    print("You should not use for loop or the word for anywhere in this exercise")

# This is the first line of the exercise
task = input()
# <eoi>

content1 = content.split("# <eoi>")[0]
print(content1)

if "while " not in content1:
    print("You should use while loop in this exercise")
if "while " in content1:
    print("You should not use while loop anywhere except in the exercise")

if task == "sum_until_0":
    total = 0
    n = int(input())

    while n != 0:
        total += n
        n = int(input())
        if n == 0:
            break
        total += n
        n = int(input())
    print(total)


    while n != 0:
        total += n
        n = int(input())
    print(total)

elif task == "total_price":
    total_price = 0
    while True:
        line = input()
        if line == "END":
            break
        quantity, price = line.split()
        quantity, price = int(quantity), int(price)
        total_price += quantity * price
    print(total_price)

elif task == "only_ed_or_ing":
    line = input()
    while line.lower() != "stop":
        if line.lower().endswith("ed") or line.lower().endswith("ing"):
            print(line)
        line = input()

elif task == "reverse_sum_palindrome":
    line = input()
    while line != "-1":
        n = int(line)
        rev = int(line[::-1])
        total = str(n + rev)
        if total == total[::-1]:
            print(n)
        line = input()

elif task == "double_string":
    line = input()
    while line != "":
        print(line * 2)
        line = input()

elif task == "odd_char":
    results = []
    line = input()
    while True:
        temp = ""
        i = 0
        while i < len(line):
            temp += line[i]
            i += 2
        results.append(temp)
        if line.endswith("."):
            break
        line = input()
    result_line = ""
    j = 0
    while j < len(results):
        result_line += results[j] + (" " if j < len(results) - 1 else "")
        j += 1
    print(result_line)

elif task == "only_even_squares":
    line = input()
    while line != "NAN":
        num = int(line)
        if (num * num) % 2 == 0:
            print(num * num)
        line = input()

elif task == "only_odd_lines":
    lines = []
    cnt = 1
    line = input()
    while line != "END":
        if cnt % 2 == 1:
            lines.insert(0, line)
        cnt += 1
        line = input()
    result = ""
    k = 0
    while k < len(lines):
        result += lines[k] + ("\n" if k < len(lines) - 1 else "")
        k += 1
    print(result)


