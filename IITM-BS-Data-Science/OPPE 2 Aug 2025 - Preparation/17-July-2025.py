with open(__file__) as f:
    content = f.read().split("# eoi")[0]

if content.count("for")>6:
    print("more than 6 for loops not allowed")

# eoi

print("content : ",content))
print("content.center : ",content.center)
print("content.strip : ",content.strip())

sentence="dummy sentence"
character1 = "d"
character2 = "u"
character3 = "m"
character4 = "m"
character5 = "y"
character6 = "s"
character7 = "e"

# eoi

for character1 in sentence:
    a=1

for character2 in sentence:
    a=1

for character3 in sentence:
    a=1

for character4 in sentence:
    a=1

for character5 in sentence:
    a=1

for character6 in sentence:
    a=1

for character7 in sentence:
    a=1

# eoi