# Question: Write a function that takes a list of integers and returns the count of odd numbers that have exactly 3 digits (including negative numbers). The absolute value should be considered for counting digits.
# Example Input: [123, 456, 789, 12, 345, 6789, -123, -456]
# Expected Output: 3

# def count_three_digit_odd_number(list_of_nums:list) -> int:
#     return sum(1 for i in list_of_nums if len(str(abs(i))) == 3 and i % 2 != 0)

# print(count_three_digit_odd_number([123, 456, 789, 12, 345, 6789, -123, -456]))  # Output: 3


# # This function checks if an element is in the first half of the first list and the second half of the second list.
# def elem_first_list_first_half_second_list_second_half(elem,l1:list,l2:list):

#         mid1 = len(l1) // 2
#         mid2 = len(l2) // 2

#         return ((elem in l1[:mid1] and elem in l2[mid2:]) or (elem in l1[mid1:] and elem in l2[:mid2]))

# # Example usage:
# print(elem_first_list_first_half_second_list_second_half(3, [1, 2, 3, 4], [5, 6, 7, 8]))  # Output: True
# print(elem_first_list_first_half_second_list_second_half(5, [1, 2, 3, 4], [5, 6, 7, 8]))  # Output: False

# ############################################################################

# Question: Write a function that checks if a given domain string ends with either '.com' or '.in'.
# Example Input: "example.com", "example.in", "example.org"
# Expected Output: True, True, False

# def domain_endswith_com_in(domain:str):
#     return domain.endswith('.com') or domain.endswith('.in')

# print("example.com : ",domain_endswith_com_in("example.com"))  # True
# print("example.in : ",domain_endswith_com_in("example.in"))  # True
# print("example.org : ",domain_endswith_com_in("example.org"))  # False

# #############################################################################

# Question: Implement a function that checks if a given number is both even and has exactly two digits (10-99).
# Example Input: 22, 43, -18, 100
# Expected Output: True, False, False, False

# def is_even_two_digit_number(num) :
#     """
#     This function checks if a number is a two-digit even number.
    
#     :param num: The number to check.
#     :return: True if the number is a two-digit even number, False otherwise.
#     """
#     return 10 <= num <= 99 and num % 2 == 0

# print("22 : ",is_even_two_digit_number(22))
# print("43 : ",is_even_two_digit_number(43))
# print("-18 : ",is_even_two_digit_number(-18))
# print("100 : ",is_even_two_digit_number(100))


# Question: Most Frequent Numbers
# Write a program that finds all numbers with the highest frequency in a list. The program should:

# Take input for the size of list (n)
# Take n integers as input
# Find all numbers that appear the maximum number of times
# Return these numbers in sorted order

# from collections import Counter
# nums = [int(input()) for i in range(int(input()))]

# value_counts = dict(Counter(nums))
# print("type(Counter(nums)) : ", type(Counter(nums)))
# print("Counter(nums) : ", Counter(nums))
# print("Value counts:", value_counts)
# print("type(value_counts) : ", type(value_counts))
    
# max_frequency = max(value_counts.values())

# most_common_values = sorted([
#     value
#     for value, count in value_counts.items()
#     if count == max_frequency
# ])

# print(most_common_values)

# Question: Write a function that finds the missing number in a sequence from 1 to n given a list of numbers.
# def missingNumber(nums:list,n:int) ->int:
#         return (set(range(1,n)) - set(nums)).pop()

# print(missingNumber([1,2,4,5],11))



# Question: Create a function that checks if str2 can be obtained by rotating str1. A rotation involves moving characters from the beginning to the end.
# Example Input: str1="abcde", str2="cdeab"
# Expected Output: True

# def rotateString(str1:str,str2:str) -> bool:
#         return len(str1) == len(str2) and str1 in 2*str2

# print(rotateString("abcde","`cdeab"))  # Output: True
# print(rotateString("abcde","abced"))   # Output: False



# Question: Write a program that prints a pattern where each line contains a pair of slashes (/ \) inside vertical bars (|), with appropriate spacing. The pattern should grow wider as it goes down.
# Example Input: n=3
# Expected Output:
# |  /\  |
# | /  \ |
# |/    \|

# n=int(input())

# for t in range(n):
#         sidespace=" " * (n - t - 1)
#         print(f"|{sidespace}/{midspace}\{sidespace}|")

# Expected Output: ['a', '1', 'b', '2', 'c', '3', 'd', '4']






# from itertools import zip_longest

# a = ('a','b','c','d')
# b = ('1','2','3','4')   


# interleaved = [x for pair in zip(a, b) for x in pair if x is not None]

# print(interleaved)
# Output: [1, 2, 3, 4, 6, 8]

# n=int(input())

# for t in range(n):
#         sidespace = ' ' * (n - t - 1)
#         midspace =  ' ' * (t * 1)
#         print(f"|{sidespace}/{midspace}\{sidespace}|")
