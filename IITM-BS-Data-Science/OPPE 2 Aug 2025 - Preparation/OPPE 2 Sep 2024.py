# def count_three_digit_odd_number(list_of_nums:list) -> int:
#     return sum(1 for i in list_of_nums if len(str(abs(i))) == 3 and i % 2 != 0)

# count_three_digit_odd_number([123, 456, 789, 12, 345, 6789, -123, -456])  # Output: 3


# # This function checks if an element is in the first half of the first list and the second half of the second list.
# def elem_first_list_first_half_second_list_second_half(elem,l1:list,l2:list):

#         mid1 = len(l1) // 2
#         mid2 = len(l2) // 2

#         return ((elem in l1[:mid1] and elem in l2[mid2:]) or (elem in l1[mid1:] and elem in l2[:mid2]))

# # Example usage:
# print(elem_first_list_first_half_second_list_second_half(3, [1, 2, 3, 4], [5, 6, 7, 8]))  # Output: True
# print(elem_first_list_first_half_second_list_second_half(5, [1, 2, 3, 4], [5, 6, 7, 8]))  # Output: False

#############################################################################

# def domain_endswith_com_in(domain:str):
#     return domain.endswith('.com') or domain.endswith('.in')

# print("example.com : ",domain_endswith_com_in("example.com"))  # True
# print("example.in : ",domain_endswith_com_in("example.in"))  # True
# print("example.org : ",domain_endswith_com_in("example.org"))  # False

##############################################################################

# def is_even_two_digit_number(num) :
#     """
#     This function checks if a number is a two-digit even number.
    
#     :param num: The number to check.
#     :return: True if the number is a two-digit even number, False otherwise.
#     """
#     return 10 <= num <= 99 and num % 2 == 0

# print("22 : ",is_even_two_digit_number(22))
# print("43 : ",is_even_two_digit_number(43))
# print("-18 : ",is_even_two_digit_number(-18))
# print("100 : ",is_even_two_digit_number(100))

