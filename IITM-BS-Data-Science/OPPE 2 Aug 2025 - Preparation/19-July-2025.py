# Write a program that takes a string input containing multiple words and outputs each word followed by the count of vowels in that word in parentheses. The program should:

# Take a line of text as input
# Count vowels (both uppercase and lowercase: a, e, i, o, u) in each word
# Output each word followed by its vowel count in parentheses
# Maintain spaces between words in the output

# def count_vowels(word):
#     vowels = "AEIOUaeiou"
#     return sum(1 for char in word if char in vowels)

# input_string = input()

# result = [f"{word}({count_vowels(word)})" for word in input_string.split()]

# # Print the output while maintaining spaces
# print(" ".join(result))


# from collections import Counter
# nums = [int(input()) for i in range(int(input()))]

# value_counts = dict(Counter(nums))
# print("type(Counter(nums)) : ", type(Counter(nums)))
# print("Counter(nums) : ", Counter(nums))
# print("Value counts:", value_counts)
# print("type(value_counts) : ", type(value_counts))
    
# max_frequency = max(value_counts.values())

# most_common_values = sorted([
#     value
#     for value, count in value_counts.items()
#     if count == max_frequency
# ])

# print(most_common_values)

# Write a function word_append_prepend() that does the following:

# Takes number of lines (n) as input
# Reads n lines of text
# Processes the lines based on their index:
# For even indices (0,2,4..): append the word at the end
# For odd indices (1,3,5..): prepend the word at the beginning
# Returns the final concatenated string

# def word_append_prepend():

# 	n = int(input("enter no. of lines input"))
# 	word = ""
	
# 	for i in range(n):
# 		if i % 2 == 0:
# 			word = word + input()
# 		else:
# 			word = input() + word
	
# 	return word

# print(word_append_prepend( ))  # Example usage

# Write a function count_vowels_and_consonants_in_even_indices that:

# Takes a string as input
# Counts vowels and consonants that appear at even indices (0, 2, 4, etc.)
# Returns a tuple containing (vowel_count, consonant_count)
# Considers only alphabetic characters and treats input case-insensitively
# Requirements:

# Use string slicing to get even-indexed characters
# Count only a, e, i, o, u as vowels (case insensitive)
# Count only alphabetic characters as consonants
# Return results as a tuple of two integers
# Ignore non-alphabetic characters in counting

# def count_vowels_and_consonants_in_even_indices(s:str):

# 	vowel_count=0
# 	consontant_count=0

# 	for char in s[::2].lower():
# 		if char in 'aeiou':
# 			vowel_count += 1
# 		elif char.isalpha():
# 			consontant_count += 1

# 	return vowel_count,consontant_count

# print(type(count_vowels_and_consonants_in_even_indices("HelloWorld")))  # Output: <class 'tuple'>
# print(count_vowels_and_consonants_in_even_indices("HelloWorld"))  # Output: (2, 3)

# Write a function number_with_more_unique_digits that:

# Takes two integers as input parameters
# Returns the number with more unique digits
# If both numbers have the same number of unique digits, return both numbers as a tuple
# The function should handle positive integers of any length

# def number_with_more_unique_digits(num1:int,num2:int):

# 	if len(set(str(num1))) > len(set(str(num2))):
# 		return num1
# 	elif len(set(str(num2))) > len(set(str(num1))):
# 		return num2
# 	else:
# 		return (num1,num2)
	
# print(number_with_more_unique_digits(1234, 5678))  # Output: (1234, 5678)
# print(number_with_more_unique_digits(1234, 1234))  # Output: (1234, 1234)
# print(number_with_more_unique_digits(1234, 12345))  # Output: 12345
# print(number_with_more_unique_digits(123, 4567))  # Output: 4567
# print(number_with_more_unique_digits(12345, 6789))  # Output: (12345, 6789)


# def surround_first_two_and_last_two_with_brackets(s1:str) -> str:
# 	return (f"[{s1[:2]}]{s1[2:-2]}[{s1[-2:]}]") if len(s1) >= 4 else s1

# print(surround_first_two_and_last_two_with_brackets("HelloWorld"))  # Output: "[He]llo[ld]"

# Write a function sum_of_floored_to_tens that:

# Takes two integers as input parameters
# Floors each number to the nearest tens (rounds down to multiple of 10)
# Returns the sum of the floored numbers

# def sum_of_floored_to_tens(a:int,b:int):
# 	a = a // 10 * 10
# 	b = b // 10 * 10
	
# 	return a+b

# print(sum_of_floored_to_tens(123, 456))  # Output: 12000

#Write a program that reads a text file, modifies it by adding "ub" or "dub" before vowels alternately, and writes the result back to the same file. The program should:

#Read a text file containing multiple lines
# Process each character in the file:
# If it's a vowel (a, e, i, o, u - case insensitive):
# Add "ub" before odd-numbered vowels
# Add "dub" before even-numbered vowels
# If it's not a vowel, keep it unchanged
# Print the modified text to console
# Write the modified text back to the same file
# filename = "sample_vowels.txt"

# # Read and process the file
# with open(filename, 'r') as f:
#     lines = f.readlines()

# # Process and write back to the same file
# with open(filename, 'w') as f:
#     vowel_count = 0
#     for line in lines:
#         output = ""
#         for char in line:
#             if char.lower() in "aeiou":
#                 output += ("ub" if vowel_count % 2 == 0 else "dub") + char
#                 vowel_count += 1
#             else:
#                 output += char
#         print(output, end="")  # Print to console
#         f.write(output)        # Write to file

# Write a program to process a grocery list and calculate various metrics. Create the following functions:

# item_amount(item: dict) -> float:

# Takes a dictionary containing item details (price and quantity)
# Returns the total amount for that item (price × quantity)
# total_bill_amount(grocery_list: list) -> float:

# Takes a list of dictionaries containing item details
# Returns the total bill amount for all items

# def item_amount(item):
#     return item['price'] * item['quantity']

# def total_bill_amount(grocery_list: list) -> float:
#     total_amount = 0
#     for item in grocery_list:
#         total_amount += item_amount(item)
#     return total_amount

# def max_quantity_item(grocery_list: list) -> str:
#     return max(grocery_list, key=lambda x: x['quantity'])['name']

# def sort_by_total_amount(grocery_list: list) -> list:
#     sorted_items = sorted(
#         grocery_list,
#         key=lambda x: (-item_amount(x),x['name'])
#     )
#     return sorted_items

# def process_grocery_list(grocery_list: list, request: str):
#     """Process the grocery list as per the request.

#     Args:
#         grocery_list (list[dict]) - A list of dictionary with the keys
#             "name", "quantity" and "price", where "price" is the amount of
#             one unit of the item.
#         request: (str) - A string containing one of the following request.
#             - 'total_bill_amount'
#             - 'max_quantity_item'
#             - 'sort_by_total_amount'

#     Returns:
#         The output corresponding to the request.
#     """
#     if request == 'total_bill_amount':
#         return total_bill_amount(grocery_list)
#     elif request == 'max_quantity_item':
#         return max_quantity_item(grocery_list)
#     elif request == 'sort_by_total_amount':
#         return sort_by_total_amount(grocery_list)
#     else:
#         return None

# grocery_list = [
#     {'name': 'apple', 'quantity': 2, 'price': 50},
#     {'name': 'banana', 'quantity': 3, 'price': 20},
#     {'name': 'orange', 'quantity': 1, 'price': 30},             
#     {'name': 'grapes', 'quantity': 5, 'price': 10},
#     {'name': 'kiwi', 'quantity': 4, 'price': 15}
# ]

# print("Total bill amount:", process_grocery_list(grocery_list, 'total_bill_amount'))
# print("Item with maximum quantity:", process_grocery_list(grocery_list, 'max_quantity_item'))
# print("Sorted grocery list by total amount:", process_grocery_list(grocery_list, 'sort_by_total_amount'))

# Output:
# Total bill amount: 290
# Item with maximum quantity: grapes
# Sorted grocery list by total amount: [{'name': 'apple', 'quantity': 2, 'price': 50}, {'name': 'banana', 'quantity': 3, 'price': 20}, {'name': 'orange', 'quantity': 1, 'price': 30}, {'name': 'kiwi', 'quantity': 4, 'price': 15}, {'name': 'grapes', 'quantity': 5, 'price': 10}]    

# Write a function initials_lastname that:

# Takes a full name as a string input
# Formats the name so that:
# All first/middle names are converted to initials followed by periods
# Last name remains unchanged
# Initials are separated by periods and spaces
# A space separates the initials from the last name
# Returns the formatted string

# def initials_lastname(full_name: str) -> str:
#     names = full_name.split()
#     initials = '. '.join(name[0] for name in names[:-1])
#     return f"{initials}. {names[-1]}" if initials else names[-1]

# # Test cases
# print(initials_lastname("Yuvaraj Aravindan"))  # Output: "Y. Aravindan"
# print(initials_lastname("John Doe Smith"))  # Output: "J. D. Smith"
# print(initials_lastname("Smith"))  # Output: "Smith"

# Takes a full name as a string input
# Formats the name so that:
# All first/middle names are converted to initials followed by periods
# Last name remains unchanged
# Initials are separated by periods and spaces
# A space separates the initials from the last name
# Returns the formatted string

# def missingNumbers(nums1: list, nums2: list) -> tuple:
#     diff1 = set(nums1) - set(nums2)
#     diff2 = set(nums2) - set(nums1)
#     return diff1, diff2

# print(missingNumbers([1, 3, 4, 5], [1, 2, 3, 5]))
# # Output: ({4}, {2})


# Takes two dictionaries as input parameters
# Merges them according to the following rules:
# For keys present in both dictionaries, add their values
# For keys present in only one dictionary, keep the original value
# Returns the merged dictionary

# def merge_dictionaries(d1: dict, d2: dict):
#     d3 = {}
#     # Add all keys from d1
#     for key in d1:
#         if key in d2:
#             d3[key] = d1[key] + d2[key]
#         else:
#             d3[key] = d1[key]
#     # Add keys from d2 that are not in d1
#     for key in d2:
#         if key not in d1:
#             d3[key] = d2[key]
#     print(d3)

# merge_dictionaries({'a': 1, 'b': 2, 'd': 5}, {'a': 3, 'b': 4, 'c': 5})

# Write a function rotateString that:

# Takes two strings as input parameters
# Checks if str2 can be obtained by rotating str1
# Returns a boolean indicating if one string is a rotation of the other

# def rotateString(str1:str,str2:str) -> bool:
#         return len(str1) == len(str2) and str1 in 2*str2

# print(rotateString("abcde","`cdeab"))  # Output: True
# print(rotateString("abcde","abced"))   # Output: False


# Write a program that prints a pattern of slashes inside vertical bars. The pattern should:

# Take an integer n as input (height of pattern)
# For each line t from 0 to n-1:
# Print side spaces (decreasing)
# Print forward slash (/)
# Print middle spaces (increasing)
# Print backslash ()
# Enclose pattern in vertical bars (|)

# n=int(input())

# for t in range(n):
#         sidespace=" " * (n - t - 1)
#         midspace=" " * 2 * t
#         print(f"|{sidespace}/{midspace}\{sidespace}|")
#         print("n : ",n," t : ",t, " sidespace : ",sidespace, " midspace : ",midspace)


# Write a program that interleaves two tuples of equal length, ensuring that the output contains all elements from both tuples in alternating order. The program should:
# Define two tuples a and b of equal length
# Interleave the elements of the tuples into a single list

# from itertools import zip_longest

# a = ('a','b','c','d')
# b = ('1','2','3','4')   

# interleaved = [x for pair in zip(a, b) for x in pair if x is not None]

# print(interleaved)
# Output: [1, 2, 3, 4, 6, 8]

# Write a program that prints a pattern of slashes inside vertical bars. The pattern should:

# Take an integer n as input (height of pattern)
# For each line t from 0 to n-1:
# Print side spaces (decreasing)
# Print forward slash (/)
# Print middle spaces (increasing)
# Print backslash ()
# Enclose pattern in vertical bars (|)

# n=int(input())

# for t in range(n):
#         sidespace = ' ' * (n - t - 1)
#         midspace =  ' ' * (t * 1)
#         print(f"|{sidespace}/{midspace}\{sidespace}|")
#         print("n : ",n," t : ",t, " sidespace : ",sidespace, " midspace : ",midspace)

# Write a program that filters students based on their average marks and other criteria. The program should:
# Define a dictionary with student names as keys and their marks as values
# Implement a function filterStudent that takes the dictionary and a criteria string as input
# The criteria can be:
# "excellent" - students with an average mark of 85 or above
# "good" - students with an average mark between 50 and 85 (exclusive)
# "all_pass" - students who have passed all subjects (marks >= 50)
# "balanced" - students whose marks do not vary by more than 10 points


# data = {
# "Alice": [90,80,85],
# "Bob": [40,50,60],
# "Charlie": [30,40,20],
# "Ram": [78,92,85,79,81],
# "Babu": [67,70,75],
# "Kumar": [100,100,100,100,100,100,40]
# }

# def average(marks):
#         return sum(marks)/len(marks)

# def filterStudent(data:dict, criteria:str) -> set:
        
#         if criteria == "excellent":
#                 return {
#                         name for name,marks in data.items()
#                                 if average(marks) >= 85
#                         }
#         elif criteria == "good":
#                 return {
#                         name for name,marks in data.items()
#                                 if average(marks) >= 50 and average(marks) < 85
#                         }
#         elif criteria == "all_pass":
#                 return {
#                         name for name,marks in data.items() 
#                                 if all(map(key=lambda x: x >= 50, marks))
#                         }
#         elif criteria == "balanced":
#                 return {
#                         name for name,marks in data.items()
#                                 if max(marks) - min(marks) <= 10
#                         }

# print(filterStudent(data,"excellent"))
# print(filterStudent(data,"good"))
# print(filterStudent(data,"all_pass"))
# print(filterStudent(data,"balanced"))


# Write a function missingNumbers that:

# Takes two lists of integers as input parameters
# Returns a tuple containing two sets:
# First set: Numbers present in nums1 but missing from nums2
# Second set: Numbers present in nums2 but missing from nums1

# def missingNumbers(nums1: list, nums2: list) -> tuple:
#     diff1 = set(nums1) - set(nums2)
#     diff2 = set(nums2) - set(nums1)
#     return diff1, diff2

# print(missingNumbers([1, 3, 4, 5], [1, 2, 3, 5]))
# # Output: ({4}, {2})
